// import type { HttpContext } from '@adonisjs/core/http'

import { HttpContext } from "@adonisjs/core/http";
import { checkText, dd, responseData } from "../function/helper.js";
import Tiezi from "#models/tiezi";
import Zan from "#models/zan";
import Huifu from "#models/huifu";
import db from "@adonisjs/lucid/services/db";
import Shoucang from "#models/shoucang";
import FcGameHistoryV2 from "#models/game/fc_game_history_v2";
import UserTopic from "#models/user_topic";
import NotificationService from '#services/notification_service';
import { NotificationSourceType } from "#models/notification";
import Report from "#models/report";
import Cache from "../function/Cache.js";
import Config from "#models/config";

export default class TiezisController {
    async fatie({ request, params, response }: HttpContext) {
        let user = request.user;
        let images = request.input('images') ? request.input('images').toString() : '';
        let text = request.input('text');
        let game_id = +request.input('game_id') || 0;
        let type = +request.input('type') || 0;
        let bankuai = +request.input('bankuai') || 0;
        let title = request.input('title') || "";

        // if (type == 1 && title == "") {
        //     return responseData(null, 400, "请输入标题");
        // }


        let checkTextResult = await checkText(text);
        let status = 0;
        if (checkTextResult) {
            status = checkTextResult.isSensitive ? 2 : 1;
        }

        let officialUserConfig = await Config.get('official_user');
        if (!officialUserConfig) {
            officialUserConfig = [];
        }
        let is_official = officialUserConfig.includes(user.uid) ? 1 : 0;

        let tiezi = await Tiezi.create({
            user_id: user.uid,
            text: text,
            images: images,
            game_id: game_id,
            type: type,
            biaoqian: '',
            bankuai: bankuai,
            title: title,
            status: status,
            is_official: is_official,
        });
        await tiezi.setImagesArr();

        if (status == 2) {
            await NotificationService.createSensitiveNotification({
                userId: user.uid,
                tieziId: tiezi.id,
                sourceType: NotificationSourceType.TIEZI,
            });
        }

        return responseData(tiezi, 200);
    }


    async list({ request, params, response }: HttpContext) {
        let page = +request.input('page') || 1;
        let type = request.input('type');
        let game_id = request.input('game_id');
        let bankuai = request.input('bankuai');
        let zhuanqu = request.input('zhuanqu');
        let sort = request.input('sort', 'new');
        let huati = request.input('huati');

        let youtu = request.input('youtu');

        let official = request.input('official');


        let query = Tiezi.query().where('status', 1);

        if (type !== undefined) query.where('type', +type);
        if (game_id !== undefined) query.where(query => query.where('game_id', +game_id).orWhere('show_in_all_topics', 1));
        if (bankuai !== undefined) query.where('bankuai', +bankuai);
        if (official !== undefined) query.where('is_official', 1);

        if (youtu !== undefined) {
            query.where('images', '!=', '');
        }

        // 专区筛选
        if (zhuanqu !== undefined) {
            if (zhuanqu == 1) {
                query.where('game_id', '>', 0);
            }
        }

        if (sort == 'hot') {
            query = query.orderBy('last_huifu_time', 'desc');
        }
        if (sort == 'last_huifu_time') {
            query = query.orderBy('last_huifu_time', 'desc');
        }
        if (sort == 'new') {
            query = query.orderBy('id', 'desc');
        }


        let list = (await query.paginate(page, 10)).all();

        await Promise.all(list.map(async (tiezi) => {
            if (request.user?.uid) {
                await tiezi.setUserZan(request.user?.uid);
                await tiezi.setShoucang(request.user?.uid);
            }
        }));

        return responseData(list, 200);
    }

    async zan({ request, params, response }: HttpContext) {
        let user = request.user;
        let type = request.input('type');
        let data_id = request.input('data_id');

        // 校验 type 和 data_id
        if (!type || !data_id) {
            return responseData(null, 400, "参数错误");
        }


        let exists = await Zan.query().where('data_id', data_id).where('user_id', user.uid).where('type', type).first();

        if (exists) {
            exists.delete();
            if (type == 1) {
                await Tiezi.query().where('id', data_id).decrement('zan_num', 1);
            } else if (type == 2) {
                await Huifu.query().where('id', data_id).decrement('zan_num', 1);
            }
            return responseData(null, 200);
        }

        let zan = await Zan.create({
            user_id: user.uid,
            data_id: data_id,
            type: type
        });

        if (type == 1) {
            await Tiezi.query().where('id', data_id).increment('zan_num', 1);
            // 获取帖子信息并发送通知
            const tiezi = await Tiezi.find(data_id);
            if (tiezi && tiezi.user_id !== user.uid) {
                await NotificationService.createLikeNotification({
                    userId: tiezi.user_id,
                    fromUserId: user.uid,
                    tieziId: tiezi.id,
                    huifuId: 0,
                    sourceType: 'tiezi'
                });
            }
        } else if (type == 2) {
            await Huifu.query().where('id', data_id).increment('zan_num', 1);
            // 获取回复信息并发送通知
            const huifu = await Huifu.find(data_id);
            if (huifu && huifu.user_id !== user.uid) {
                await NotificationService.createLikeNotification({
                    userId: huifu.user_id,
                    fromUserId: user.uid,
                    tieziId: huifu.tiezi_id,
                    huifuId: huifu.id,
                    sourceType: 'huifu'
                });
            }
        }

        return responseData(zan, 200);
    }

    async shoucang({ request, params, response }: HttpContext) {
        let user = request.user;
        let tiezi_id = request.input('tiezi_id');

        // 校验 tiezi_id以及帖子是否存在
        if (!tiezi_id) {
            return responseData(null, 400, "参数错误");
        }

        let tiezi = await Tiezi.query().where('id', tiezi_id).first();
        if (!tiezi) {
            return responseData(null, 400, "帖子不存在");
        }


        let exists = await Shoucang.query().where('tiezi_id', tiezi_id).where('user_id', user.uid).first();

        if (exists) {
            exists.delete();
            return responseData(null, 200);
        }

        let shoucang = await Shoucang.create({
            user_id: user.uid,
            tiezi_id: tiezi_id,
        });

        return responseData(shoucang, 200);
    }

    async imageList({ request, params, response }: HttpContext) {
        let images = request.input('id').toString().split(',').map(Number);

        // Create a cache key based on the image IDs
        const cacheKey = `images:${images.join(',')}`;

        // Try to get data from cache first
        let list = await Cache.get(cacheKey);

        // If not in cache, fetch from database and store in cache
        if (!list) {
            list = await db.connection('flash_center').from('cos').whereIn('id', images);
            // Cache for 5 minutes (300000 milliseconds)
            await Cache.set(cacheKey, list, 300000);
        }

        return responseData(list, 200);
    }

    async detail({ request, params, response }: HttpContext) {
        let id = request.input('id');
        let tiezi = await Tiezi.query().where('id', id).first();
        if (!tiezi) {
            return responseData(null, 400, "帖子不存在");
        }

        if (tiezi.status != 1) {
            return responseData(null, 400, "帖子不存在");
        }

        await tiezi.setUserZan(request.user?.uid);
        await tiezi.setShoucang(request.user?.uid);
        return responseData(tiezi, 200);
    }

    async delete({ request, params, response }: HttpContext) {
        let user = request.user;
        let id = request.input('id');
        let tiezi = await Tiezi.query().where('id', id).andWhere('user_id', user.uid).first();
        if (!tiezi) {
            return responseData(null, 400, "帖子不存在");
        }
        tiezi.status = 0;
        await tiezi.save();
        return responseData(null, 200);
    }

    async validateHuifuParams(lou_id: number, huifu_id: number): Promise<string | null> {
        if (huifu_id === 0) {
            // 直接回复帖子
            return null;
        }

        const parent_huifu = await Huifu.query().where('id', huifu_id).first();
        if (!parent_huifu) {
            return "回复对象不存在"; // 回复对象不存在
        }

        if (parent_huifu.lou_id !== lou_id) {
            return "回复对象和楼不匹配";
        }

        return null;
    }
    async huifu({ request, params, response }: HttpContext) {
        let user = request.user;
        let tiezi_id = +request.input('tiezi_id');
        let huifu_id = +request.input('huifu_id', 0);
        let lou_id = +request.input('lou_id', 0);

        let text = request.input('text');

        let tiezi = await Tiezi.query().where('id', tiezi_id).first();
        if (!tiezi) {
            return responseData(null, 400, "帖子不存在");
        }

        let checkHuifu = await this.validateHuifuParams(lou_id, huifu_id);
        if (checkHuifu) {
            return responseData(null, 400, checkHuifu);
        }

        if (!text) {
            return responseData(null, 400, "请输入回复内容");
        }

        let checkTextResult = await checkText(text);
        if (checkTextResult === null) {
            return responseData(null, 400, "回复失败");
        }
        if (checkTextResult && checkTextResult.isSensitive) {
            return responseData(null, 400, "您的回复包含敏感词");
        }

        let huifu = await Huifu.create({
            user_id: user.uid,
            text: text,
            tiezi_id: tiezi_id,
            huifu_id: huifu_id,
            lou_id: lou_id
        });

        // 顶楼的 lou_id 设置为自己的id
        if (huifu.huifu_id == 0) {
            huifu.lou_id = huifu.id;
            huifu.save();

            // 如果是回复帖子，给帖子作者发送通知
            if (tiezi.user_id !== user.uid) {
                await NotificationService.createReplyNotification({
                    userId: tiezi.user_id,
                    fromUserId: user.uid,
                    fromHuifuId: huifu.id,
                    tieziId: tiezi.id,
                    huifuId: 0,
                    sourceType: NotificationSourceType.TIEZI,
                    content: text.substring(0, 100) // 取前100个字符作为通知内容
                });
            }
        } else {
            // 如果是回复评论，给评论作者发送通知
            const parentHuifu = await Huifu.find(huifu_id);
            if (parentHuifu && parentHuifu.user_id !== user.uid) {
                await NotificationService.createReplyNotification({
                    userId: parentHuifu.user_id,
                    fromUserId: user.uid,
                    fromHuifuId: huifu.id,
                    tieziId: tiezi.id,
                    huifuId: parentHuifu.id,
                    sourceType: NotificationSourceType.HUIFU,
                    content: text.substring(0, 100) // 取前100个字符作为通知内容
                });
            }
        }

        await Tiezi.query().where('id', tiezi_id).increment('huifu_num', 1);
        await Tiezi.query().where('id', tiezi_id).update({
            last_huifu_time: new Date()
        });

        return responseData(huifu, 200);
    }

    async deleteHuifu({ request, params, response }: HttpContext) {
        let user = request.user;
        let id = request.input('id');
        let huifu = await Huifu.query().where('id', id).andWhere('user_id', user.uid).first();
        if (!huifu) {
            return responseData(null, 400, "回复不存在");
        }
        await huifu.delete();
        await Tiezi.query().where('id', huifu.tiezi_id).decrement('huifu_num', 1);
        return responseData(null, 200);
    }

    async huifuList({ request, params, response }: HttpContext) {
        let tiezi_id = request.input('tiezi_id');
        let list = (await Huifu.query().where('tiezi_id', tiezi_id)
            .orderBy('id', 'desc'));
        const filteredHuifus: Huifu[] = [];
        for (const huifu of list) {
            // 如果回复的用户信息不存在，则过滤掉该回复
            if (huifu.userinfo) {
                filteredHuifus.push(huifu);
            }
        }
        list = filteredHuifus;

        let result: any = [];
        let map: any = {};
        for (let i = 0; i < list.length; i++) {
            let huifu = list[i];
            map[huifu.id] = huifu;
        }

        for (let huifu of list) {
            await huifu.setUserZan(request.user?.uid);

            if (huifu.huifu_id === 0) {
                result.push(huifu);
            } else if (map[huifu.lou_id]) {
                map[huifu.lou_id].huifuList.push(huifu);
            }
        }

        return responseData(result, 200);
    }


    public async topicList({ request, response }: HttpContext) {
        const user = request.user

        let defaultTopicList = [{ game_id: 1106, hot: 1 }, { game_id: 1614, hot: 1 }, { game_id: 1615, hot: 1 }]

        // 如果用户未登录，直接返回默认热门游戏列表
        if (!user) {
            return responseData(defaultTopicList, 200)
        }

        // 1. 优先从用户自定义的话题记录中获取
        let userTopicList = await this.getUserDefinedTopicList(user.uid)

        // 如果用户没有设置话题记录，创建默认话题记录
        if (userTopicList === null) {
            userTopicList = []
            const recentGameIds = await this.getRecentGameIds(user.uid)
            if (recentGameIds && recentGameIds.length > 0) {
                // 将最近游戏id映射为话题对象，hot标记为0
                userTopicList = recentGameIds.map(id => ({ game_id: parseInt(id), play: 1 }));
            }
            // 如果最近游戏数量不足3个，则用默认话题补全
            if (userTopicList.length < 3) {
                for (let topic of defaultTopicList) {
                    if (userTopicList.length >= 3) break;
                    // 避免重复添加
                    if (!userTopicList.some(item => item.game_id === topic.game_id)) {
                        userTopicList.push(topic);
                    }
                }
            }
            // 将结果保存到用户自定义的话题记录中
            let userTopic = new UserTopic()
            userTopic.user_id = user.uid
            userTopic.topic_list = JSON.stringify(userTopicList)
            await userTopic.save()
        }

        // 最后返回默认热门游戏列表
        return responseData(userTopicList, 200)
    }

    /**
     * 获取用户自定义的话题列表记录
     * @param userId 用户ID
     * @returns 如果用户有设置，则返回话题列表，否则返回空数组
     */
    private async getUserDefinedTopicList(userId: number): Promise<any[] | null> {
        const userTopic = await UserTopic.query().where('user_id', userId).first()

        if (!userTopic) {
            return null
        }

        try {
            const topicList = JSON.parse(userTopic.topic_list)
            if (Array.isArray(topicList)) {
                return topicList
            } else {
                console.error(`用户话题记录格式错误: ${userTopic.topic_list}`)
            }
        } catch (error) {
            console.error("解析用户话题记录时出错: ", error)
        }

        return []
    }

    /**
     * 获取用户最近玩的游戏ID（最多返回3个）
     * @param userId 用户ID
     * @returns 最近玩的游戏ID数组
     */
    private async getRecentGameIds(userId: number): Promise<string[]> {
        const userGames = await FcGameHistoryV2.getUserData(userId)
        if (!userGames) {
            return []
        }
        return Object.keys(userGames)
            .sort((a, b) => userGames[b].time - userGames[a].time)
            .slice(0, 3)
    }

    // 在类TiezisController中添加一个私有函数ngram2
    private ngram2(text: string): string[] {
        const result: string[] = [];
        for (let i = 0; i < text.length - 1; i++) {
            result.push(text.substring(i, i + 2));
        }
        return result;
    }

    /**
     * 搜索帖子
     * @param request 请求对象
     * @param response 响应对象
     * @returns 搜索结果
     */
    async search({ request, response }: HttpContext) {

        // 获取搜索关键词
        const keyword = request.input('keyword')
        if (!keyword || keyword.trim() === '') {
            return responseData([], 400, '请输入搜索关键词')
        }

        // 进行ngram=2分词
        const inputKeyword = keyword.trim();
        const tokens = this.ngram2(inputKeyword);
        const processedKeyword = tokens.join(' ');

        // 获取分页参数
        const page = +request.input('page') || 1
        const limit = +request.input('limit') || 10

        // 获取筛选参数
        const type = request.input('type')
        const game_id = request.input('game_id')
        const bankuai = request.input('bankuai')
        const sort = request.input('sort', 'new')

        // 构建查询，使用分词后的processedKeyword进行全文搜索
        let query = Tiezi.query()
            .select('*', db.raw('MATCH(title, text) AGAINST(? IN NATURAL LANGUAGE MODE) as score', [processedKeyword]))
            .where('status', 1)
            .whereRaw("MATCH(title, text) AGAINST(? IN NATURAL LANGUAGE MODE)", [processedKeyword])

        // 应用筛选条件
        if (type !== undefined) query.where('type', +type)
        if (game_id !== undefined) query.where('game_id', +game_id)
        if (bankuai !== undefined) query.where('bankuai', +bankuai)

        query = query.orderBy('score', 'desc')
        // 应用排序
        if (sort === 'hot') {
            query = query.orderBy('last_huifu_time', 'desc')
        }

        // 执行查询并分页
        const results = (await query.paginate(page, limit)).all();

        // 设置用户点赞和收藏状态
        await Promise.all(results.map(async (tiezi) => {
            if (request.user?.uid) {
                await tiezi.setUserZan(request.user.uid)
                await tiezi.setShoucang(request.user.uid)
            }
        }))

        return responseData(results, 200)
    }

    async topicUpdate({ request, response }: HttpContext) {
        const user = request.user
        if (!user) {
            return responseData(false, 400, "用户未登录")
        }

        const data = request.input('data')

        if (!data) {
            return responseData(false, 400, "参数错误")
        }
        let topicList = JSON.parse(atob(data))
        if (!Array.isArray(topicList)) {
            return responseData(false, 400, "参数错误")
        }

        let userTopic = await UserTopic.query()
            .where('user_id', user.uid)
            .first()

        if (!userTopic) {
            userTopic = new UserTopic()
            userTopic.user_id = user.uid
        }
        userTopic.topic_list = JSON.stringify(topicList)
        await userTopic.save()

        return responseData(true, 200)
    }

    async report({ request, response }: HttpContext) {
        const user = request.user;
        const targetId = +request.input('id');
        const targetType = +request.input('type') || 1; // 默认为帖子
        const reason = request.input('reason') || '';

        // 参数验证
        if (!targetId) {
            return responseData(false, 400, "目标ID不能为空");
        }

        // 验证目标是否存在
        let target;
        if (targetType === 1) {
            // 帖子
            target = await Tiezi.find(targetId);
            if (!target) {
                return responseData(false, 404, "帖子不存在");
            }
        } else if (targetType === 2) {
            // 回复
            target = await Huifu.find(targetId);
            if (!target) {
                return responseData(false, 404, "回复不存在");
            }
        } else {
            return responseData(false, 400, "目标类型错误");
        }

        // 检查是否已经举报过
        const existingReport = await Report.query()
            .where('user_id', user.uid)
            .where('target_id', targetId)
            .where('target_type', targetType)
            .first();

        if (existingReport) {
            return responseData(false, 400, "您已经举报过该内容");
        }

        // 创建举报记录
        const report = await Report.create({
            user_id: user.uid,
            target_id: targetId,
            target_type: targetType,
            reason: reason,
            status: 0, // 待处理
        });

        return responseData(true, 200, "举报成功");
    }

}

module.exports = {
	apps: [
		{
			name: "sqapi-dev",
			script: "./ace.js",
			args: 'serve -w',
			env: {
				NODE_ENV: "dev",
			},
		}, {
			name: "sqapi-test",
			script: "./build/bin/server.js",
			//script : "./index.js",
			cwd: "/app/data/site/sqapi-flash-cn",
			instances: 1,
			env: {
				NODE_ENV: "test",
			},
			error_file: "/var/log/pm2/sqapi_error.log",
			out_file: "/var/log/pm2/sqapi_out.log",
			merge_logs: true,
			log_date_format: "YYYY-MM-DD HH:mm:ss ",
		}, {
			name: "sqapi-stage",
			script: "./build/bin/server.js",
			cwd: "/app/data/site/sqapi-flash-cn",
			instances: 1,
			env: {
				NODE_ENV: "stage",
			},
			error_file: "/var/log/pm2/sqapi_error.log",
			out_file: "/var/log/pm2/sqapi_out.log",
			merge_logs: true,
			log_date_format: "YYYY-MM-DD HH:mm:ss ",
		}, {
			name: "sqapi-prod",
			script: "./build/bin/server.js",
			cwd: "/app/data/site/sqapi-flash-cn",
			instances: "max",
			env: {
				NODE_ENV: "prod",
			},
			error_file: "/var/log/pm2/sqapi_error.log",
			out_file: "/var/log/pm2/sqapi_out.log",
			merge_logs: true,
			log_date_format: "YYYY-MM-DD HH:mm:ss ",
		}
	]
}
